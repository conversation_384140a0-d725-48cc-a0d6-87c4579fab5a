using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Sub.JakeCRM.Infrastructure.Configuration;
using Sub.JakeCRM.Domain.Interfaces;
using System.Diagnostics;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Sub.JakeCRM.Infrastructure.Http;

/// <summary>
/// HTTP client implementation for making API calls
/// </summary>
public class ApiClient : IApiClient, IDisposable
{
    private readonly HttpClient _httpClient;
    private readonly ApiClientOptions _options;
    private readonly ILogger<ApiClient> _logger;
    private bool _disposed;

    public ApiClient(HttpClient httpClient, IOptions<ApiClientOptions> options, ILogger<ApiClient> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        ConfigureHttpClient();
    }

    public async Task<ApiResponse> SubmitAsync(string endpoint, object data, Dictionary<string, string>? headers = null, CancellationToken cancellationToken = default)
    {
        var jsonData = JsonSerializer.Serialize(data, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        });

        return await SubmitJsonAsync(endpoint, jsonData, headers, cancellationToken);
    }

    public async Task<ApiResponse> SubmitJsonAsync(string endpoint, string jsonData, Dictionary<string, string>? headers = null, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestId = Guid.NewGuid().ToString("N")[..8];

        try
        {
            _logger.LogInformation("Starting API request {RequestId} to {Endpoint}", requestId, endpoint);

            if (_options.EnableDetailedLogging)
            {
                var logData = jsonData.Length > _options.MaxLogContentLength 
                    ? jsonData[.._options.MaxLogContentLength] + "..." 
                    : jsonData;
                _logger.LogDebug("Request {RequestId} payload: {Payload}", requestId, logData);
            }

            using var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
            request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

            // Add custom headers
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                    {
                        request.Content.Headers.ContentType = MediaTypeHeaderValue.Parse(header.Value);
                    }
                    else
                    {
                        request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }
            }

            // Add request ID for tracing
            request.Headers.TryAddWithoutValidation("X-Request-Id", requestId);

            var response = await SendWithRetryAsync(request, cancellationToken);
            stopwatch.Stop();

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var isSuccess = response.IsSuccessStatusCode;

            if (_options.EnableDetailedLogging)
            {
                var logContent = responseContent.Length > _options.MaxLogContentLength 
                    ? responseContent[.._options.MaxLogContentLength] + "..." 
                    : responseContent;
                _logger.LogDebug("Response {RequestId} ({StatusCode}): {Content}", 
                    requestId, (int)response.StatusCode, logContent);
            }

            var apiResponse = new ApiResponse
            {
                IsSuccess = isSuccess,
                StatusCode = (int)response.StatusCode,
                Content = responseContent,
                ResponseTime = stopwatch.Elapsed,
                Headers = response.Headers.ToDictionary(h => h.Key, h => string.Join(", ", h.Value))
            };

            if (!isSuccess)
            {
                apiResponse.ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
                _logger.LogWarning("API request {RequestId} failed with status {StatusCode}: {ReasonPhrase}", 
                    requestId, response.StatusCode, response.ReasonPhrase);
            }
            else
            {
                _logger.LogInformation("API request {RequestId} completed successfully in {Duration}ms", 
                    requestId, stopwatch.ElapsedMilliseconds);
            }

            return apiResponse;
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            stopwatch.Stop();
            _logger.LogError("API request {RequestId} timed out after {Duration}ms", requestId, stopwatch.ElapsedMilliseconds);
            
            return new ApiResponse
            {
                IsSuccess = false,
                StatusCode = 408,
                ErrorMessage = "Request timeout",
                ResponseTime = stopwatch.Elapsed
            };
        }
        catch (HttpRequestException ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "HTTP error for request {RequestId}", requestId);
            
            return new ApiResponse
            {
                IsSuccess = false,
                StatusCode = 0,
                ErrorMessage = ex.Message,
                ResponseTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Unexpected error for request {RequestId}", requestId);
            
            return new ApiResponse
            {
                IsSuccess = false,
                StatusCode = 0,
                ErrorMessage = ex.Message,
                ResponseTime = stopwatch.Elapsed
            };
        }
    }

    private async Task<HttpResponseMessage> SendWithRetryAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        HttpResponseMessage? response = null;
        Exception? lastException = null;

        for (int attempt = 0; attempt <= _options.RetryCount; attempt++)
        {
            try
            {
                // Clone the request for retry attempts
                var requestToSend = attempt == 0 ? request : await CloneRequestAsync(request);
                
                response = await _httpClient.SendAsync(requestToSend, cancellationToken);

                // If successful or client error (4xx), don't retry
                if (response.IsSuccessStatusCode || ((int)response.StatusCode >= 400 && (int)response.StatusCode < 500))
                {
                    return response;
                }

                // Server error (5xx) - retry
                if (attempt < _options.RetryCount)
                {
                    _logger.LogWarning("Request failed with status {StatusCode}, retrying in {Delay}ms (attempt {Attempt}/{MaxAttempts})", 
                        response.StatusCode, _options.RetryDelay.TotalMilliseconds, attempt + 1, _options.RetryCount + 1);
                    
                    response.Dispose();
                    await Task.Delay(_options.RetryDelay, cancellationToken);
                }
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                lastException = ex;
                
                if (attempt < _options.RetryCount)
                {
                    _logger.LogWarning(ex, "Request failed with exception, retrying in {Delay}ms (attempt {Attempt}/{MaxAttempts})", 
                        _options.RetryDelay.TotalMilliseconds, attempt + 1, _options.RetryCount + 1);
                    
                    await Task.Delay(_options.RetryDelay, cancellationToken);
                }
            }
        }

        return response ?? throw (lastException ?? new HttpRequestException("Request failed after all retry attempts"));
    }

    private static async Task<HttpRequestMessage> CloneRequestAsync(HttpRequestMessage original)
    {
        var clone = new HttpRequestMessage(original.Method, original.RequestUri);
        
        if (original.Content != null)
        {
            var content = await original.Content.ReadAsStringAsync();
            clone.Content = new StringContent(content, Encoding.UTF8, original.Content.Headers.ContentType?.MediaType ?? "application/json");
        }

        foreach (var header in original.Headers)
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        return clone;
    }

    private void ConfigureHttpClient()
    {
        if (!string.IsNullOrWhiteSpace(_options.BaseUrl))
        {
            _httpClient.BaseAddress = new Uri(_options.BaseUrl);
        }

        _httpClient.Timeout = _options.Timeout;

        // Add default headers
        foreach (var header in _options.DefaultHeaders)
        {
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
        }

        // Add authentication headers
        if (!string.IsNullOrWhiteSpace(_options.ApiKey))
        {
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation(_options.ApiKeyHeaderName, _options.ApiKey);
        }

        if (!string.IsNullOrWhiteSpace(_options.BearerToken))
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _options.BearerToken);
        }

        // Add user agent
        _httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Sub.JakeCRM.ServiceHost/1.0");
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _httpClient?.Dispose();
            _disposed = true;
        }
    }
}
