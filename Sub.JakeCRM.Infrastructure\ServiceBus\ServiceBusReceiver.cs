using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Sub.JakeCRM.Infrastructure.Configuration;
using Sub.JakeCRM.Domain.Entities;
using Sub.JakeCRM.Domain.Interfaces;

namespace Sub.JakeCRM.Infrastructure.ServiceBus;

/// <summary>
/// Implementation of Service Bus message receiver
/// </summary>
public class ServiceBusReceiver : IServiceBusReceiver, IDisposable
{
    private readonly ServiceBusOptions _options;
    private readonly ILogger<ServiceBusReceiver> _logger;
    private ServiceBusClient? _client;
    private ServiceBusProcessor? _processor;

    private bool _disposed;

    public ServiceBusReceiver(IOptions<ServiceBusOptions> options, ILogger<ServiceBusReceiver> logger)
    {
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        if (string.IsNullOrWhiteSpace(_options.ConnectionString))
            throw new ArgumentException("Service Bus connection string is required", nameof(options));
    }

    public async Task StartReceivingAsync(Func<Domain.Entities.ServiceBusMessage, CancellationToken, Task<ProcessingResult>> messageHandler, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting Service Bus receiver...");

            _client = new ServiceBusClient(_options.ConnectionString);

            var processorOptions = new ServiceBusProcessorOptions
            {
                MaxConcurrentCalls = _options.MaxConcurrentCalls,
                PrefetchCount = _options.PrefetchCount,
                AutoCompleteMessages = false, // We'll handle completion manually
                ReceiveMode = ServiceBusReceiveMode.PeekLock
            };

            // Create processor based on configuration (queue vs topic/subscription)
            if (!string.IsNullOrWhiteSpace(_options.QueueName))
            {
                _processor = _client.CreateProcessor(_options.QueueName, processorOptions);
                _logger.LogInformation("Created processor for queue: {QueueName}", _options.QueueName);
            }
            else if (!string.IsNullOrWhiteSpace(_options.TopicName) && !string.IsNullOrWhiteSpace(_options.SubscriptionName))
            {
                _processor = _client.CreateProcessor(_options.TopicName, _options.SubscriptionName, processorOptions);
                _logger.LogInformation("Created processor for topic: {TopicName}, subscription: {SubscriptionName}", 
                    _options.TopicName, _options.SubscriptionName);
            }
            else
            {
                throw new InvalidOperationException("Either QueueName or both TopicName and SubscriptionName must be specified");
            }

            // Set up message and error handlers
            _processor.ProcessMessageAsync += async args =>
            {
                try
                {
                    var message = ConvertToServiceBusMessage(args.Message);
                    var result = await messageHandler(message, args.CancellationToken);

                    if (result.IsSuccess)
                    {
                        await args.CompleteMessageAsync(args.Message, args.CancellationToken);
                        _logger.LogDebug("Message {MessageId} completed successfully", message.MessageId);
                    }
                    else
                    {
                        if (args.Message.DeliveryCount >= _options.MaxDeliveryCount)
                        {
                            await args.DeadLetterMessageAsync(args.Message, 
                                "MaxDeliveryCountExceeded", 
                                result.ErrorMessage ?? "Message processing failed after maximum retry attempts",
                                args.CancellationToken);
                            _logger.LogWarning("Message {MessageId} dead lettered after {DeliveryCount} attempts", 
                                message.MessageId, args.Message.DeliveryCount);
                        }
                        else
                        {
                            await args.AbandonMessageAsync(args.Message, cancellationToken: args.CancellationToken);
                            _logger.LogWarning("Message {MessageId} abandoned for retry. Attempt {DeliveryCount}", 
                                message.MessageId, args.Message.DeliveryCount);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in message handler for message {MessageId}", args.Message.MessageId);
                    
                    try
                    {
                        if (args.Message.DeliveryCount >= _options.MaxDeliveryCount)
                        {
                            await args.DeadLetterMessageAsync(args.Message, 
                                "ProcessingException", 
                                ex.Message,
                                args.CancellationToken);
                        }
                        else
                        {
                            await args.AbandonMessageAsync(args.Message, cancellationToken: args.CancellationToken);
                        }
                    }
                    catch (Exception handlerEx)
                    {
                        _logger.LogError(handlerEx, "Error handling failed message {MessageId}", args.Message.MessageId);
                    }
                }
            };

            _processor.ProcessErrorAsync += args =>
            {
                _logger.LogError(args.Exception, "Service Bus processor error. Source: {ErrorSource}, Entity: {EntityPath}", 
                    args.ErrorSource, args.EntityPath);
                return Task.CompletedTask;
            };

            await _processor.StartProcessingAsync(cancellationToken);
            _logger.LogInformation("Service Bus receiver started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Service Bus receiver");
            throw;
        }
    }

    public async Task StopReceivingAsync()
    {
        try
        {
            if (_processor != null)
            {
                _logger.LogInformation("Stopping Service Bus receiver...");
                await _processor.StopProcessingAsync();
                _logger.LogInformation("Service Bus receiver stopped");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Service Bus receiver");
            throw;
        }
    }

    public async Task CompleteMessageAsync(string lockToken)
    {
        // Note: Message completion is handled automatically in the processor message handler
        // This method is provided for interface compliance but is not used in this implementation
        _logger.LogDebug("CompleteMessageAsync called with lockToken: {LockToken}. Message completion is handled in processor.", lockToken);
        await Task.CompletedTask;
    }

    public async Task AbandonMessageAsync(string lockToken)
    {
        // Note: Message abandonment is handled automatically in the processor message handler
        // This method is provided for interface compliance but is not used in this implementation
        _logger.LogDebug("AbandonMessageAsync called with lockToken: {LockToken}. Message abandonment is handled in processor.", lockToken);
        await Task.CompletedTask;
    }

    public async Task DeadLetterMessageAsync(string lockToken, string reason, string errorDescription)
    {
        // Note: Dead lettering is handled automatically in the processor message handler
        // This method is provided for interface compliance but is not used in this implementation
        _logger.LogDebug("DeadLetterMessageAsync called with lockToken: {LockToken}. Dead lettering is handled in processor.", lockToken);
        await Task.CompletedTask;
    }

    private static Domain.Entities.ServiceBusMessage ConvertToServiceBusMessage(Azure.Messaging.ServiceBus.ServiceBusReceivedMessage receivedMessage)
    {
        return new Domain.Entities.ServiceBusMessage
        {
            MessageId = receivedMessage.MessageId,
            Body = receivedMessage.Body.ToString(),
            Subject = receivedMessage.Subject ?? string.Empty,
            ContentType = receivedMessage.ContentType ?? string.Empty,
            CorrelationId = receivedMessage.CorrelationId ?? string.Empty,
            EnqueuedTime = receivedMessage.EnqueuedTime,
            ExpiresAt = receivedMessage.ExpiresAt,
            SessionId = receivedMessage.SessionId ?? string.Empty,
            ReplyTo = receivedMessage.ReplyTo ?? string.Empty,
            ReplyToSessionId = receivedMessage.ReplyToSessionId ?? string.Empty,
            PartitionKey = receivedMessage.PartitionKey ?? string.Empty,

            TimeToLive = receivedMessage.TimeToLive,
            DeliveryCount = receivedMessage.DeliveryCount,
            LockToken = receivedMessage.LockToken,
            LockedUntil = receivedMessage.LockedUntil,
            SequenceNumber = receivedMessage.SequenceNumber,
            ApplicationProperties = receivedMessage.ApplicationProperties.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _processor?.DisposeAsync().AsTask().Wait();
            _client?.DisposeAsync().AsTask().Wait();
            _disposed = true;
        }
    }
}
