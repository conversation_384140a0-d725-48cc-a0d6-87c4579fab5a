using Microsoft.Extensions.Logging;
using Sub.JakeCRM.Domain.Entities;
using Sub.JakeCRM.Domain.Interfaces;
using System.Diagnostics;
using System.Text.Json;

namespace Sub.JakeCRM.Application.Services;

/// <summary>
/// Service responsible for processing messages from Service Bus
/// </summary>
public class MessageProcessorService : IMessageProcessor
{
    private readonly IApiClient _apiClient;
    private readonly ILogger<MessageProcessorService> _logger;

    public MessageProcessorService(IApiClient apiClient, ILogger<MessageProcessorService> logger)
    {
        _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ProcessingResult> ProcessMessageAsync(ServiceBusMessage message, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Processing message {MessageId} with subject {Subject}", 
                message.MessageId, message.Subject);

            // Validate message
            if (string.IsNullOrWhiteSpace(message.Body))
            {
                _logger.LogWarning("Message {MessageId} has empty body", message.MessageId);
                return ProcessingResult.Failure("Message body is empty", null, 0, 
                    new Dictionary<string, object> { ["MessageId"] = message.MessageId });
            }

            // Parse message body to determine destination endpoint
            var destinationEndpoint = DetermineDestinationEndpoint(message);
            if (string.IsNullOrWhiteSpace(destinationEndpoint))
            {
                _logger.LogWarning("Could not determine destination endpoint for message {MessageId}", message.MessageId);
                return ProcessingResult.Failure("Could not determine destination endpoint", null, 0,
                    new Dictionary<string, object> { ["MessageId"] = message.MessageId });
            }

            // Prepare headers
            var headers = PrepareHeaders(message);

            // Submit to destination API
            var apiResponse = await _apiClient.SubmitJsonAsync(destinationEndpoint, message.Body, headers, cancellationToken);

            stopwatch.Stop();

            if (apiResponse.IsSuccess)
            {
                _logger.LogInformation("Successfully processed message {MessageId} in {Duration}ms", 
                    message.MessageId, stopwatch.ElapsedMilliseconds);
                
                return ProcessingResult.Success(stopwatch.Elapsed, new Dictionary<string, object>
                {
                    ["MessageId"] = message.MessageId,
                    ["StatusCode"] = apiResponse.StatusCode,
                    ["ResponseTime"] = apiResponse.ResponseTime.TotalMilliseconds
                });
            }
            else
            {
                _logger.LogError("Failed to process message {MessageId}. Status: {StatusCode}, Error: {Error}", 
                    message.MessageId, apiResponse.StatusCode, apiResponse.ErrorMessage);
                
                return ProcessingResult.Failure(
                    $"API call failed with status {apiResponse.StatusCode}", 
                    apiResponse.ErrorMessage, 
                    0,
                    new Dictionary<string, object>
                    {
                        ["MessageId"] = message.MessageId,
                        ["StatusCode"] = apiResponse.StatusCode,
                        ["ResponseContent"] = apiResponse.Content ?? string.Empty
                    });
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error processing message {MessageId}", message.MessageId);
            
            return ProcessingResult.Failure(
                "Unexpected error during message processing", 
                ex.ToString(), 
                0,
                new Dictionary<string, object> { ["MessageId"] = message.MessageId });
        }
    }

    private string DetermineDestinationEndpoint(ServiceBusMessage message)
    {
        // Try to get endpoint from application properties first
        if (message.ApplicationProperties.TryGetValue("DestinationEndpoint", out var endpointObj) && 
            endpointObj is string endpoint && !string.IsNullOrWhiteSpace(endpoint))
        {
            return endpoint;
        }

        // Try to get endpoint from subject
        if (!string.IsNullOrWhiteSpace(message.Subject))
        {
            return $"/api/{message.Subject.ToLowerInvariant()}";
        }

        // Default endpoint
        return "/api/messages";
    }

    private Dictionary<string, string> PrepareHeaders(ServiceBusMessage message)
    {
        var headers = new Dictionary<string, string>
        {
            ["Content-Type"] = !string.IsNullOrWhiteSpace(message.ContentType) ? message.ContentType : "application/json",
            ["X-Message-Id"] = message.MessageId,
            ["X-Correlation-Id"] = message.CorrelationId ?? Guid.NewGuid().ToString(),
            ["X-Enqueued-Time"] = message.EnqueuedTime.ToString("O"),
            ["X-Processing-Time"] = DateTimeOffset.UtcNow.ToString("O")
        };

        if (!string.IsNullOrWhiteSpace(message.Subject))
        {
            headers["X-Message-Subject"] = message.Subject;
        }

        if (!string.IsNullOrWhiteSpace(message.SessionId))
        {
            headers["X-Session-Id"] = message.SessionId;
        }

        // Add custom application properties as headers (with X-Custom- prefix)
        foreach (var prop in message.ApplicationProperties.Where(p => p.Value is string))
        {
            headers[$"X-Custom-{prop.Key}"] = prop.Value.ToString()!;
        }

        return headers;
    }
}
