{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.ServiceHost\\Sub.JakeCRM.ServiceHost.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\Sub.JakeCRM.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\Sub.JakeCRM.Application.csproj", "projectName": "Sub.JakeCRM.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\Sub.JakeCRM.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\Sub.JakeCRM.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\Sub.JakeCRM.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.5, )"}, "NLog": {"target": "Package", "version": "[5.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\Sub.JakeCRM.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\Sub.JakeCRM.Domain.csproj", "projectName": "Sub.JakeCRM.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\Sub.JakeCRM.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Infrastructure\\Sub.JakeCRM.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Infrastructure\\Sub.JakeCRM.Infrastructure.csproj", "projectName": "Sub.JakeCRM.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Infrastructure\\Sub.JakeCRM.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\Sub.JakeCRM.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\Sub.JakeCRM.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\Sub.JakeCRM.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Domain\\Sub.JakeCRM.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Azure.Messaging.ServiceBus": {"target": "Package", "version": "[7.19.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.ServiceHost\\Sub.JakeCRM.ServiceHost.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.ServiceHost\\Sub.JakeCRM.ServiceHost.csproj", "projectName": "Sub.JakeCRM.ServiceHost", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.ServiceHost\\Sub.JakeCRM.ServiceHost.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.ServiceHost\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\Sub.JakeCRM.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Application\\Sub.JakeCRM.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Infrastructure\\Sub.JakeCRM.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Sub.JakeCRM\\Sub.JakeCRM.Infrastructure\\Sub.JakeCRM.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[9.0.5, )"}, "NLog.Extensions.Hosting": {"target": "Package", "version": "[5.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}