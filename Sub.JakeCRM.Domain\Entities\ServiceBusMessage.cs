namespace Sub.JakeCRM.Domain.Entities;

/// <summary>
/// Represents a message received from Service Bus
/// </summary>
public class ServiceBusMessage
{
    public string MessageId { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public string CorrelationId { get; set; } = string.Empty;
    public DateTimeOffset EnqueuedTime { get; set; }
    public DateTimeOffset ExpiresAt { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public string ReplyTo { get; set; } = string.Empty;
    public string ReplyToSessionId { get; set; } = string.Empty;
    public string PartitionKey { get; set; } = string.Empty;
    public string ViaPartitionKey { get; set; } = string.Empty;
    public TimeSpan TimeToLive { get; set; }
    public int DeliveryCount { get; set; }
    public string LockToken { get; set; } = string.Empty;
    public DateTimeOffset LockedUntil { get; set; }
    public long SequenceNumber { get; set; }
    public Dictionary<string, object> ApplicationProperties { get; set; } = new();
}
