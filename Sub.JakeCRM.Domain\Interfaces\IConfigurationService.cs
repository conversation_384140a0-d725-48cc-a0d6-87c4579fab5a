using Sub.JakeCRM.Domain.Entities;

namespace Sub.JakeCRM.Domain.Interfaces;

/// <summary>
/// Service for managing application configuration
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// Get configuration value by key
    /// </summary>
    T GetValue<T>(string key, T defaultValue = default!);

    /// <summary>
    /// Get configuration section as dictionary
    /// </summary>
    Dictionary<string, string> GetSection(string sectionName);

    /// <summary>
    /// Get strongly typed configuration options
    /// </summary>
    T GetOptions<T>() where T : class, new();

    /// <summary>
    /// Check if configuration key exists
    /// </summary>
    bool <PERSON><PERSON>(string key);

    /// <summary>
    /// Get all configuration as dictionary
    /// </summary>
    Dictionary<string, string> GetAllSettings();

    /// <summary>
    /// Validate configuration settings
    /// </summary>
    ConfigurationValidationResult ValidateConfiguration();

    /// <summary>
    /// Reload configuration from sources
    /// </summary>
    void ReloadConfiguration();
}
