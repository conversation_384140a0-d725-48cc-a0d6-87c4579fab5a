namespace Sub.JakeCRM.Infrastructure.Configuration;

/// <summary>
/// Security configuration options
/// </summary>
public class SecurityOptions
{
    public const string SectionName = "Security";

    /// <summary>
    /// Enable message encryption at rest
    /// </summary>
    public bool EnableEncryption { get; set; } = false;

    /// <summary>
    /// Encryption key or key vault reference
    /// </summary>
    public string? EncryptionKey { get; set; }

    /// <summary>
    /// Encryption algorithm (AES256, etc.)
    /// </summary>
    public string EncryptionAlgorithm { get; set; } = "AES256";

    /// <summary>
    /// Enable message signing for integrity verification
    /// </summary>
    public bool EnableMessageSigning { get; set; } = false;

    /// <summary>
    /// Signing key or certificate reference
    /// </summary>
    public string? SigningKey { get; set; }

    /// <summary>
    /// Enable API authentication validation
    /// </summary>
    public bool EnableApiAuthentication { get; set; } = true;

    /// <summary>
    /// API authentication type (<PERSON><PERSON><PERSON><PERSON>, Bearer, Certificate, etc.)
    /// </summary>
    public string ApiAuthenticationType { get; set; } = "ApiKey";

    /// <summary>
    /// Enable certificate-based authentication
    /// </summary>
    public bool EnableCertificateAuthentication { get; set; } = false;

    /// <summary>
    /// Client certificate configuration
    /// </summary>
    public CertificateOptions? ClientCertificate { get; set; }

    /// <summary>
    /// Enable Azure Key Vault integration
    /// </summary>
    public bool EnableKeyVault { get; set; } = false;

    /// <summary>
    /// Azure Key Vault configuration
    /// </summary>
    public KeyVaultOptions? KeyVault { get; set; }

    /// <summary>
    /// Enable audit logging for security events
    /// </summary>
    public bool EnableAuditLogging { get; set; } = true;

    /// <summary>
    /// Audit log retention period in days
    /// </summary>
    public int AuditLogRetentionDays { get; set; } = 90;

    /// <summary>
    /// Enable data masking for sensitive information in logs
    /// </summary>
    public bool EnableDataMasking { get; set; } = true;

    /// <summary>
    /// Fields to mask in logs (JSON paths or property names)
    /// </summary>
    public List<string> MaskedFields { get; set; } = new()
    {
        "password",
        "secret",
        "token",
        "key",
        "ssn",
        "creditCard"
    };

    /// <summary>
    /// Enable IP address filtering
    /// </summary>
    public bool EnableIpFiltering { get; set; } = false;

    /// <summary>
    /// Allowed IP addresses or ranges
    /// </summary>
    public List<string> AllowedIpAddresses { get; set; } = new();

    /// <summary>
    /// Enable rate limiting for API calls
    /// </summary>
    public bool EnableRateLimiting { get; set; } = false;

    /// <summary>
    /// Rate limiting configuration
    /// </summary>
    public RateLimitingOptions? RateLimiting { get; set; }
}

/// <summary>
/// Certificate configuration options
/// </summary>
public class CertificateOptions
{
    /// <summary>
    /// Certificate store location (CurrentUser, LocalMachine)
    /// </summary>
    public string StoreLocation { get; set; } = "CurrentUser";

    /// <summary>
    /// Certificate store name (My, Root, etc.)
    /// </summary>
    public string StoreName { get; set; } = "My";

    /// <summary>
    /// Certificate thumbprint
    /// </summary>
    public string? Thumbprint { get; set; }

    /// <summary>
    /// Certificate subject name
    /// </summary>
    public string? SubjectName { get; set; }

    /// <summary>
    /// Certificate file path (for file-based certificates)
    /// </summary>
    public string? FilePath { get; set; }

    /// <summary>
    /// Certificate password (for password-protected certificates)
    /// </summary>
    public string? Password { get; set; }
}

/// <summary>
/// Azure Key Vault configuration options
/// </summary>
public class KeyVaultOptions
{
    /// <summary>
    /// Key Vault URL
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// Client ID for Key Vault authentication
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// Client secret for Key Vault authentication
    /// </summary>
    public string? ClientSecret { get; set; }

    /// <summary>
    /// Tenant ID for Key Vault authentication
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// Use managed identity for Key Vault authentication
    /// </summary>
    public bool UseManagedIdentity { get; set; } = true;

    /// <summary>
    /// Key Vault secret names to load
    /// </summary>
    public List<string> SecretNames { get; set; } = new();

    /// <summary>
    /// Cache secrets locally for performance
    /// </summary>
    public bool CacheSecrets { get; set; } = true;

    /// <summary>
    /// Secret cache expiry in minutes
    /// </summary>
    public int CacheExpiryMinutes { get; set; } = 60;
}

/// <summary>
/// Rate limiting configuration options
/// </summary>
public class RateLimitingOptions
{
    /// <summary>
    /// Maximum requests per time window
    /// </summary>
    public int MaxRequests { get; set; } = 100;

    /// <summary>
    /// Time window in seconds
    /// </summary>
    public int TimeWindowSeconds { get; set; } = 60;

    /// <summary>
    /// Rate limiting strategy (FixedWindow, SlidingWindow, TokenBucket)
    /// </summary>
    public string Strategy { get; set; } = "SlidingWindow";

    /// <summary>
    /// Enable rate limiting per client/source
    /// </summary>
    public bool EnablePerClientLimiting { get; set; } = false;

    /// <summary>
    /// Client identification method (IpAddress, ApiKey, etc.)
    /// </summary>
    public string ClientIdentificationMethod { get; set; } = "IpAddress";
}
