<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="C:/logs/internal-nlog.txt">

  <!-- Enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Extensions.Logging" />
  </extensions>

  <!-- Define variables -->
  <variable name="logDirectory" value="C:/logs" />
  <variable name="applicationName" value="Sub.JakeCRM.ServiceHost" />
  
  <!-- Define layout patterns -->
  <variable name="defaultLayout" 
            value="${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}" />
  
  <variable name="detailedLayout" 
            value="${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] [${threadid}] ${message} ${exception:format=tostring}" />
  
  <variable name="jsonLayout" 
            value='{"timestamp":"${longdate}","level":"${level:uppercase=true}","logger":"${logger}","thread":"${threadid}","message":"${message}","exception":"${exception:format=tostring}","properties":{"application":"${applicationName}","environment":"${environment:ASPNETCORE_ENVIRONMENT}","machineName":"${machinename}","processId":"${processid}"}}' />

  <!-- Define targets -->
  <targets>
    
    <!-- Console target for development -->
    <target xsi:type="Console" 
            name="console"
            layout="${time} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}" />
    
    <!-- File target for all logs -->
    <target xsi:type="File"
            name="allfile"
            fileName="${logDirectory}/all-${shortdate}.log"
            layout="${defaultLayout}"
            archiveFileName="${logDirectory}/archives/all-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true" />
    
    <!-- File target for application logs only -->
    <target xsi:type="File"
            name="appfile"
            fileName="${logDirectory}/app-${shortdate}.log"
            layout="${detailedLayout}"
            archiveFileName="${logDirectory}/archives/app-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true" />
    
    <!-- File target for errors only -->
    <target xsi:type="File"
            name="errorfile"
            fileName="${logDirectory}/errors-${shortdate}.log"
            layout="${detailedLayout}"
            archiveFileName="${logDirectory}/archives/errors-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="90"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true" />
    
    <!-- JSON file target for structured logging -->
    <target xsi:type="File"
            name="jsonfile"
            fileName="${logDirectory}/structured-${shortdate}.json"
            layout="${jsonLayout}"
            archiveFileName="${logDirectory}/archives/structured-{#}.json"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true" />
    
    <!-- Event log simulation using file target -->
    <target xsi:type="File"
            name="eventlog"
            fileName="${logDirectory}/eventlog-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} [${logger}] ${message} ${exception:format=tostring}"
            archiveFileName="${logDirectory}/archives/eventlog-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true" />
    
  </targets>

  <!-- Define rules -->
  <rules>
    
    <!-- Skip Microsoft logs and so log only own logs (BlackHole) -->
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <logger name="System.Net.Http.*" maxlevel="Info" final="true" />
    
    <!-- Application logs to console (development) -->
    <logger name="Sub.JakeCRM.*" minlevel="Debug" writeTo="console" />
    
    <!-- All logs to file -->
    <logger name="*" minlevel="Info" writeTo="allfile" />
    
    <!-- Application logs to dedicated file -->
    <logger name="Sub.JakeCRM.*" minlevel="Debug" writeTo="appfile" />
    
    <!-- Error logs to dedicated error file -->
    <logger name="*" minlevel="Error" writeTo="errorfile" />
    
    <!-- Structured logs to JSON file -->
    <logger name="Sub.JakeCRM.*" minlevel="Info" writeTo="jsonfile" />
    

    
    <!-- Event log for critical errors -->
    <logger name="*" minlevel="Error" writeTo="eventlog" />
    
  </rules>
  
</nlog>
