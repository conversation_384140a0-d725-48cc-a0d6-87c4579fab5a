using Microsoft.Extensions.Logging;
using NLog;

namespace Sub.JakeCRM.ServiceHost;

/// <summary>
/// Simple class to test logging functionality
/// </summary>
public static class TestLogging
{
    public static void TestAllLogLevels()
    {
        var logger = LogManager.GetCurrentClassLogger();
        
        logger.Trace("This is a TRACE message");
        logger.Debug("This is a DEBUG message");
        logger.Info("This is an INFO message");
        logger.Warn("This is a WARN message");
        logger.Error("This is an ERROR message");
        logger.Fatal("This is a FATAL message");
        
        // Test with exception
        try
        {
            throw new InvalidOperationException("Test exception for logging");
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Caught test exception");
        }
        
        // Test structured logging
        logger.Info("Processing message {MessageId} for customer {CustomerId} at {Timestamp}", 
            "MSG-12345", "CUST-67890", DateTime.UtcNow);
        
        // Force flush
        LogManager.Flush();

        // Check NLog configuration
        var config = LogManager.Configuration;
        Console.WriteLine($"NLog Configuration: {config?.AllTargets?.Count} targets");

        if (config?.AllTargets != null)
        {
            foreach (var target in config.AllTargets)
            {
                Console.WriteLine($"Target: {target.Name} ({target.GetType().Name})");
                if (target is NLog.Targets.FileTarget fileTarget)
                {
                    Console.WriteLine($"  File: {fileTarget.FileName?.Render(new NLog.LogEventInfo())}");
                }
            }
        }

        Console.WriteLine($"Current Directory: {Directory.GetCurrentDirectory()}");
        Console.WriteLine("Test logging completed. Check the logs directory for files.");
    }
}
