{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Sub.JakeCRM.Infrastructure/1.0.0": {"dependencies": {"Azure.Messaging.ServiceBus": "7.19.0", "Microsoft.Extensions.Http": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5", "Sub.JakeCRM.Application": "1.0.0", "Sub.JakeCRM.Domain": "1.0.0"}, "runtime": {"Sub.JakeCRM.Infrastructure.dll": {}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Core.Amqp/1.3.1": {"dependencies": {"Microsoft.Azure.Amqp": "2.6.9", "System.Memory": "4.5.4", "System.Memory.Data": "6.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"assemblyVersion": "1.3.1.0", "fileVersion": "1.300.124.38003"}}}, "Azure.Messaging.ServiceBus/7.19.0": {"dependencies": {"Azure.Core": "1.44.1", "Azure.Core.Amqp": "1.3.1", "Microsoft.Azure.Amqp": "2.6.9"}, "runtime": {"lib/net8.0/Azure.Messaging.ServiceBus.dll": {"assemblyVersion": "7.19.0.0", "fileVersion": "7.1900.25.20802"}}}, "Microsoft.Azure.Amqp/2.6.9": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"assemblyVersion": "2.6.0.0", "fileVersion": "2.6.9.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Http/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "NLog/5.5.0": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.5.0.3962"}}}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "6.0.10"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.46703"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Text.Json": "6.0.10"}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.10": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Sub.JakeCRM.Application/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "NLog": "5.5.0", "Sub.JakeCRM.Domain": "1.0.0"}, "runtime": {"Sub.JakeCRM.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Sub.JakeCRM.Domain/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "NLog": "5.5.0"}, "runtime": {"Sub.JakeCRM.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Sub.JakeCRM.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Core.Amqp/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-AY1ZM4WwLBb9L2WwQoWs7wS2XKYg83tp3yVVdgySdebGN0FuIszuEqCy3Nhv6qHpbkjx/NGuOTsUbF/oNGBgwA==", "path": "azure.core.amqp/1.3.1", "hashPath": "azure.core.amqp.1.3.1.nupkg.sha512"}, "Azure.Messaging.ServiceBus/7.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-DoGfZpH6bwtsA6siIS5wv3SZZzOoNLnJi9N7OHhtBvE5Wlfn13jvwEe5z92HOddJVYEchdIZBK+jHBtPz1HFpg==", "path": "azure.messaging.servicebus/7.19.0", "hashPath": "azure.messaging.servicebus.7.19.0.nupkg.sha512"}, "Microsoft.Azure.Amqp/2.6.9": {"type": "package", "serviceable": true, "sha512": "sha512-5i9XzfqxK1H5IBl+OuOV1jwJdrOvi5RUwsZgVOryZm0GCzcM9NWPNRxzPAbsSeaR2T6+1gGvdT3vR+Vbha6KFQ==", "path": "microsoft.azure.amqp/2.6.9", "hashPath": "microsoft.azure.amqp.2.6.9.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-uYXLg2Gt8KUH5nT3u+TBpg9VrRcN5+2zPmIjqEHR4kOoBwsbtMDncEJw9HiLvZqGgIo2TR4oraibAoy5hXn2bQ==", "path": "microsoft.extensions.configuration/9.0.5", "hashPath": "microsoft.extensions.configuration.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-7pQ4Tkyofm8DFWFhqn9ZmG8qSAC2VitWleATj5qob9V9KtoxCVdwRtmiVl/ha3WAgjkEfW++JLWXox9MJwMgkg==", "path": "microsoft.extensions.configuration.binder/9.0.5", "hashPath": "microsoft.extensions.configuration.binder.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-fRiUjmhm9e4vMp6WEO9MgWNxVtWSr4Pcgh1W4DyJIr8bRANlZz9JU7uicf7ShzMspDxo/9Ejo9zJ6qQZY0IhVw==", "path": "microsoft.extensions.diagnostics/9.0.5", "hashPath": "microsoft.extensions.diagnostics.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6YfTcULCYREMTqtk+s3UiszsFV2xN2FXtxdQpurmQJY9Cp/QGiM4MTKfJKUo7AzdLuzjOKKMWjQITmvtK7AsUg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.5", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-LLm+e8lvD+jOI+blHRSxPqywPaohOTNcVzQv548R1UpkEiNB2D+zf3RrqxBdB1LDPicRMTnfiaKJovxF8oX1bQ==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.5", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-3GA/dxqkP6yFe18qYRgtKYuN2onC8NfhlpNN21jptkVKk7olqBTkdT49oL0pSEz2SptRsux7LocCU7+alGnEag==", "path": "microsoft.extensions.hosting.abstractions/9.0.5", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6vbo3XjyEc+w/kv/Dkfv9NA7iSdIdX5dlU9Shk3wJJ0fiZpCVzVW5FJtNoIePX5hS0ENNpHPClq/qtq06yM4FQ==", "path": "microsoft.extensions.http/9.0.5", "hashPath": "microsoft.extensions.http.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-CJbAVdovKPFh2FoKxesu20odRVSbL/vtvzzObnG+5u38sOfzRS2Ncy25id0TjYUGQzMhNnJUHgTUzTMDl/3c9g==", "path": "microsoft.extensions.options.configurationextensions/9.0.5", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "NLog/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FCH8s7GWlonH5JXV9/EpeNJ8pRZQMVZOSWX3JrHPU8rzdHJhS5+lUGGvJIUOtzkGV1clYBFR0WXOI5FnUwVCMA==", "path": "nlog/5.5.0", "hashPath": "nlog.5.5.0.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-NSB0kDipxn2ychp88NXWfFRFlmi1bst/xynOutbnpEfRCT9JZkZ7KOmF/I/hNKo2dILiMGnqblm+j1sggdLB9g==", "path": "system.text.json/6.0.10", "hashPath": "system.text.json.6.0.10.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Sub.JakeCRM.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Sub.JakeCRM.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}