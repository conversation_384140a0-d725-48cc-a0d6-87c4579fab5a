namespace Sub.JakeCRM.Domain.Interfaces;

/// <summary>
/// Service for configuring NLog dynamically based on application configuration
/// </summary>
public interface INLogConfigurationService
{
    /// <summary>
    /// Configure NLog based on application settings
    /// </summary>
    void ConfigureNLog();

    /// <summary>
    /// Reconfigure NLog with new settings
    /// </summary>
    void ReconfigureNLog();

    /// <summary>
    /// Add custom target to NLog configuration
    /// </summary>
    void AddCustomTarget(string name, object target);

    /// <summary>
    /// Add custom logging rule
    /// </summary>
    void AddLoggingRule(string loggerNamePattern, string minLevel, string targetName);
}
