namespace Sub.JakeCRM.Domain.Entities;

/// <summary>
/// Represents the result of processing a message
/// </summary>
public class ProcessingResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorDetails { get; set; }
    public DateTimeOffset ProcessedAt { get; set; }
    public TimeSpan ProcessingDuration { get; set; }
    public int RetryCount { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    public static ProcessingResult Success(TimeSpan processingDuration, Dictionary<string, object>? metadata = null)
    {
        return new ProcessingResult
        {
            IsSuccess = true,
            ProcessedAt = DateTimeOffset.UtcNow,
            ProcessingDuration = processingDuration,
            Metadata = metadata ?? new Dictionary<string, object>()
        };
    }

    public static ProcessingResult Failure(string errorMessage, string? errorDetails = null, int retryCount = 0, Dictionary<string, object>? metadata = null)
    {
        return new ProcessingResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ErrorDetails = errorDetails,
            ProcessedAt = DateTimeOffset.UtcNow,
            RetryCount = retryCount,
            Metadata = metadata ?? new Dictionary<string, object>()
        };
    }
}
