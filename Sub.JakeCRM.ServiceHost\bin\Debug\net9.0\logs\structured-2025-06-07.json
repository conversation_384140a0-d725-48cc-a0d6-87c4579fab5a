{"timestamp":"2025-06-07 17:02:42.0825","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host initializing...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.2221","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.2510","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Starting configuration validation...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.2854","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration Summary:","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.2854","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production)","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.2976","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Service Bus: Queue=your-queue-name, Topic=, Subscription=","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3067","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3067","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Processing: Batching=False, Deduplication=False, Parallel=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3067","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Monitoring: AppInsights=False, Metrics=True, HealthChecks=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3067","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Security: Encryption=False, KeyVault=False, Audit=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3067","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration validation passed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3195","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Configuration validation completed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3195","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Name: Sub.JakeCRM.ServiceHost","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Version: 1.0.0","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Environment: Production","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:QueueName: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:TopicName: ","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:MaxConcurrentCalls: 1","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:BaseUrl: https://your-destination-api.com","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:Timeout: 00:00:30","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3344","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:RetryCount: 3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableBatching: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableParallelProcessing: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:EnableCustomMetrics: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:HealthChecks:Enabled: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableEncryption: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableAuditLogging: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== End Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.3507","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.4010","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Starting Service Bus receiver...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.4235","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Created processor for queue: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.4990","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Service Bus receiver started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:42.4990","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:02:55.0821","level":"ERROR","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"7","message":"Service Bus processor error. Source: Receive, Entity: your-queue-name","exception":"Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:03:07.5320","level":"ERROR","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"5","message":"Service Bus processor error. Source: Receive, Entity: your-queue-name","exception":"Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"19028"}}
{"timestamp":"2025-06-07 17:05:19.8198","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host initializing...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.2305","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.2686","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Starting configuration validation...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3137","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration Summary:","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3239","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production)","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3269","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Service Bus: Queue=your-queue-name, Topic=, Subscription=","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3269","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3401","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Processing: Batching=False, Deduplication=False, Parallel=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3401","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Monitoring: AppInsights=False, Metrics=True, HealthChecks=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3401","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Security: Encryption=False, KeyVault=False, Audit=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3401","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration validation passed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3545","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Configuration validation completed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3545","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3722","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Name: Sub.JakeCRM.ServiceHost","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3722","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Version: 1.0.0","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3722","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Environment: Production","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3722","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:QueueName: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3811","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:TopicName: ","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3811","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:MaxConcurrentCalls: 1","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3811","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:BaseUrl: https://your-destination-api.com","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3811","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:Timeout: 00:00:30","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3811","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:RetryCount: 3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3811","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableBatching: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3811","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableParallelProcessing: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3988","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:EnableCustomMetrics: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3988","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:HealthChecks:Enabled: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3988","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableEncryption: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3988","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableAuditLogging: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3988","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== End Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.3988","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.5044","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Starting Service Bus receiver...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.5520","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Created processor for queue: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.6469","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Service Bus receiver started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:20.6493","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:33.3305","level":"ERROR","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"10","message":"Service Bus processor error. Source: Receive, Entity: your-queue-name","exception":"Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:44.8588","level":"ERROR","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"8","message":"Service Bus processor error. Source: Receive, Entity: your-queue-name","exception":"Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:05:57.3728","level":"ERROR","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"10","message":"Service Bus processor error. Source: Receive, Entity: your-queue-name","exception":"Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"23720"}}
{"timestamp":"2025-06-07 17:09:06.2259","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host initializing...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:06.9830","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0062","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Starting configuration validation...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0366","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration Summary:","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0490","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production)","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0490","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Service Bus: Queue=your-queue-name, Topic=, Subscription=","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0637","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0637","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Processing: Batching=False, Deduplication=False, Parallel=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0693","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Monitoring: AppInsights=False, Metrics=True, HealthChecks=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0693","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Security: Encryption=False, KeyVault=False, Audit=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0693","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration validation passed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0693","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Configuration validation completed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0693","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0962","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Name: Sub.JakeCRM.ServiceHost","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.0962","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Version: 1.0.0","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1023","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Environment: Production","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1023","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:QueueName: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1023","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:TopicName: ","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1023","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:MaxConcurrentCalls: 1","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1023","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:BaseUrl: https://your-destination-api.com","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1023","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:Timeout: 00:00:30","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1160","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:RetryCount: 3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1160","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableBatching: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1160","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableParallelProcessing: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1160","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:EnableCustomMetrics: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1160","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:HealthChecks:Enabled: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1160","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableEncryption: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1160","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableAuditLogging: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1329","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== End Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1329","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.1803","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Starting Service Bus receiver...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.2072","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Created processor for queue: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.2752","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Service Bus receiver started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:07.2752","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:19.9182","level":"ERROR","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"7","message":"Service Bus processor error. Source: Receive, Entity: your-queue-name","exception":"Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"14100"}}
{"timestamp":"2025-06-07 17:09:21.8702","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host initializing...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.3166","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Program","thread":"1","message":"Sub.JakeCRM Service Host starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.3641","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Starting configuration validation...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.4430","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration Summary:","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.4577","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production)","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.4632","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Service Bus: Queue=your-queue-name, Topic=, Subscription=","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.4755","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.4755","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Processing: Batching=False, Deduplication=False, Parallel=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.4909","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Monitoring: AppInsights=False, Metrics=True, HealthChecks=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.4909","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Security: Encryption=False, KeyVault=False, Audit=True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5070","level":"INFO","logger":"Sub.JakeCRM.Application.Services.ConfigurationService","thread":"1","message":"Configuration validation passed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5070","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Configuration validation completed successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5070","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5323","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Name: Sub.JakeCRM.ServiceHost","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5323","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Version: 1.0.0","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5402","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Application:Environment: Production","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5402","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:QueueName: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5402","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:TopicName: ","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5530","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ServiceBus:MaxConcurrentCalls: 1","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5530","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:BaseUrl: https://your-destination-api.com","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5530","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:Timeout: 00:00:30","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5530","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"ApiClient:RetryCount: 3","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5530","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableBatching: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5530","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Processing:EnableParallelProcessing: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5530","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:EnableCustomMetrics: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5689","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Monitoring:HealthChecks:Enabled: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5689","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableEncryption: False","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5689","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"Security:EnableAuditLogging: True","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5689","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.ConfigurationValidationService","thread":"1","message":"=== End Configuration Summary ===","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.5878","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service starting...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.6942","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Starting Service Bus receiver...","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.7532","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Created processor for queue: your-queue-name","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.8849","level":"INFO","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"1","message":"Service Bus receiver started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:22.8879","level":"INFO","logger":"Sub.JakeCRM.ServiceHost.Services.MessageProcessingBackgroundService","thread":"1","message":"Message Processing Background Service started successfully","exception":"","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
{"timestamp":"2025-06-07 17:09:35.5214","level":"ERROR","logger":"Sub.JakeCRM.Infrastructure.ServiceBus.ServiceBusReceiver","thread":"8","message":"Service Bus processor error. Source: Receive, Entity: your-queue-name","exception":"Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)","properties":{"application":"Sub.JakeCRM.ServiceHost","environment":"","machineName":"JAKE-SURFACE","processId":"17792"}}
