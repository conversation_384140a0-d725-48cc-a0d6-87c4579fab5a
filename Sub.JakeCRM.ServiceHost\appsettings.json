{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Azure.Messaging.ServiceBus": "Warning", "System.Net.Http.HttpClient": "Warning"}}, "NLog": {"AutoReload": true, "InternalLogLevel": "Info", "EnableInternalLogging": true, "InternalLogFile": "logs/internal-nlog.txt", "LogDirectory": "logs", "MaxArchiveFiles": 30, "ArchiveEvery": "Day", "EnableConsoleLogging": true, "EnableFileLogging": true, "EnableEventLogLogging": true, "EnableJsonLogging": true, "EnableDatabaseLogging": false, "DatabaseConnectionString": null, "EnablePerformanceCounters": false, "ConsoleMinLevel": "Info", "FileMinLevel": "Info", "EventLogMinLevel": "Error", "JsonMinLevel": "Info", "EnableConcurrentWrites": true, "KeepFileOpen": false, "CustomLayout": null, "EnableCompression": false, "FileEncoding": "UTF-8", "FileBufferSize": 32768, "FlushTimeoutMs": 1000, "EnableAsyncLogging": false, "AsyncQueueLimit": 10000, "AsyncOverflowAction": "Block", "CustomTargets": [], "LoggerRules": []}, "Application": {"Name": "Sub.JakeCRM.ServiceHost", "Version": "1.0.0", "Environment": "Production", "EnablePerformanceMonitoring": true, "EnableDetailedLogging": false, "MaxMessagesPerMinute": 0, "ShutdownTimeoutSeconds": 30, "EnableHealthChecks": true, "HealthCheckIntervalSeconds": 30, "CustomProperties": {"DeploymentRegion": "East US", "ServiceTier": "Standard"}}, "ServiceBus": {"ConnectionString": "Endpoint=sb://your-servicebus-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-shared-access-key", "QueueName": "your-queue-name", "TopicName": null, "SubscriptionName": null, "MaxConcurrentCalls": 1, "PrefetchCount": 0, "MaxWaitTime": "00:00:30", "AutoCompleteMessages": true, "MaxDeliveryCount": 3, "EnableSessions": false}, "ApiClient": {"BaseUrl": "https://your-destination-api.com", "Timeout": "00:00:30", "RetryCount": 3, "RetryDelay": "00:00:02", "DefaultHeaders": {"User-Agent": "Sub.JakeCRM.ServiceHost/1.0"}, "ApiKey": null, "ApiKeyHeaderName": "X-API-Key", "BearerToken": null, "EnableDetailedLogging": false, "MaxLogContentLength": 4096}, "Processing": {"EnableMessageTransformation": false, "TransformationRules": [], "EnableMessageFiltering": false, "FilterRules": [], "EnableBatching": false, "MaxBatchSize": 10, "BatchTimeoutSeconds": 30, "EnableDeduplication": false, "DeduplicationCacheSize": 10000, "DeduplicationCacheExpiryMinutes": 60, "EnableArchiving": false, "ArchiveConnectionString": null, "ArchiveContainer": "processed-messages", "ProcessingTimeoutSeconds": 300, "EnableParallelProcessing": true, "MaxDegreeOfParallelism": 4}, "Monitoring": {"EnableApplicationInsights": false, "ApplicationInsightsKey": null, "ApplicationInsightsConnectionString": null, "EnableCustomMetrics": true, "MetricsIntervalSeconds": 60, "EnablePerformanceCounters": true, "PerformanceCounters": ["MessagesProcessed", "MessagesPerSecond", "ProcessingDuration", "ErrorRate", "ApiResponseTime"], "EnableDistributedTracing": false, "TracingEndpoint": null, "TracingServiceName": "Sub.JakeCRM.ServiceHost", "EnableStructuredLogging": true, "CorrelationIdHeaderName": "X-Correlation-ID", "EnableAlerting": false, "AlertWebhookUrl": null, "AlertErrorRateThreshold": 5.0, "AlertLatencyThresholdMs": 5000, "HealthChecks": {"Enabled": true, "Path": "/health", "DetailedPath": "/health/detailed", "TimeoutSeconds": 30, "IncludeDependencies": true, "Dependencies": [{"Name": "ServiceBus", "Type": "ServiceBus", "ConnectionString": "UseMainConnectionString", "TimeoutSeconds": 10, "IsCritical": true, "Parameters": {}}, {"Name": "DestinationAPI", "Type": "Api", "ConnectionString": "UseMainBaseUrl", "TimeoutSeconds": 10, "IsCritical": true, "Parameters": {}}]}}, "Security": {"EnableEncryption": false, "EncryptionKey": null, "EncryptionAlgorithm": "AES256", "EnableMessageSigning": false, "SigningKey": null, "EnableApiAuthentication": true, "ApiAuthenticationType": "<PERSON><PERSON><PERSON><PERSON>", "EnableCertificateAuthentication": false, "ClientCertificate": null, "EnableKeyVault": false, "KeyVault": null, "EnableAuditLogging": true, "AuditLogRetentionDays": 90, "EnableDataMasking": true, "MaskedFields": ["password", "secret", "token", "key", "ssn", "creditCard"], "EnableIpFiltering": false, "AllowedIpAddresses": [], "EnableRateLimiting": false, "RateLimiting": null}}