using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sub.JakeCRM.Domain.Entities;
using Sub.JakeCRM.Domain.Interfaces;
using System.Text.Json;

namespace Sub.JakeCRM.Application.Services;

/// <summary>
/// Implementation of configuration service
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;

    public ConfigurationService(
        IConfiguration configuration,
        ILogger<ConfigurationService> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        LogConfigurationSummary();
    }

    public T GetValue<T>(string key, T defaultValue = default!)
    {
        try
        {
            return _configuration.GetValue<T>(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get configuration value for key: {Key}. Using default value: {DefaultValue}", key, defaultValue);
            return defaultValue;
        }
    }

    public Dictionary<string, string> GetSection(string sectionName)
    {
        var section = _configuration.GetSection(sectionName);
        var result = new Dictionary<string, string>();

        foreach (var kvp in section.AsEnumerable())
        {
            if (kvp.Value != null)
            {
                result[kvp.Key] = kvp.Value;
            }
        }

        return result;
    }

    public T GetOptions<T>() where T : class, new()
    {
        var options = new T();
        var sectionName = GetSectionNameForType<T>();

        if (!string.IsNullOrEmpty(sectionName))
        {
            _configuration.GetSection(sectionName).Bind(options);
        }

        return options;
    }

    public bool HasKey(string key)
    {
        return _configuration[key] != null;
    }

    public Dictionary<string, string> GetAllSettings()
    {
        var settings = new Dictionary<string, string>();
        
        foreach (var kvp in _configuration.AsEnumerable())
        {
            if (kvp.Value != null)
            {
                // Mask sensitive values
                var maskedValue = MaskSensitiveValue(kvp.Key, kvp.Value);
                settings[kvp.Key] = maskedValue;
            }
        }

        return settings;
    }

    public ConfigurationValidationResult ValidateConfiguration()
    {
        var result = new ConfigurationValidationResult();

        try
        {
            // Validate required configuration sections exist
            ValidateRequiredSections(result);

            result.IsValid = result.Errors.Count == 0;

            if (result.IsValid)
            {
                _logger.LogInformation("Configuration validation passed successfully");
            }
            else
            {
                _logger.LogError("Configuration validation failed with {ErrorCount} errors", result.Errors.Count);
                foreach (var error in result.Errors)
                {
                    _logger.LogError("Configuration error: {Error}", error);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during configuration validation");
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
        }

        return result;
    }

    public void ReloadConfiguration()
    {
        try
        {
            if (_configuration is IConfigurationRoot configRoot)
            {
                configRoot.Reload();
                _logger.LogInformation("Configuration reloaded successfully");
                LogConfigurationSummary();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reload configuration");
        }
    }

    private void ValidateRequiredSections(ConfigurationValidationResult result)
    {
        // Validate that required configuration sections exist
        var requiredSections = new[] { "ServiceBus", "ApiClient", "Application" };

        foreach (var section in requiredSections)
        {
            var configSection = _configuration.GetSection(section);
            if (!configSection.Exists())
            {
                result.Errors.Add($"Required configuration section '{section}' is missing");
            }
        }

        // Validate critical configuration values
        ValidateCriticalValues(result);
    }

    private void ValidateCriticalValues(ConfigurationValidationResult result)
    {
        // ServiceBus validation
        var serviceBusConnectionString = _configuration["ServiceBus:ConnectionString"];
        if (string.IsNullOrWhiteSpace(serviceBusConnectionString))
        {
            result.Errors.Add("ServiceBus:ConnectionString is required");
        }

        var queueName = _configuration["ServiceBus:QueueName"];
        var topicName = _configuration["ServiceBus:TopicName"];
        var subscriptionName = _configuration["ServiceBus:SubscriptionName"];

        if (string.IsNullOrWhiteSpace(queueName) &&
            (string.IsNullOrWhiteSpace(topicName) || string.IsNullOrWhiteSpace(subscriptionName)))
        {
            result.Errors.Add("Either ServiceBus:QueueName or both ServiceBus:TopicName and ServiceBus:SubscriptionName must be specified");
        }

        // ApiClient validation
        var apiBaseUrl = _configuration["ApiClient:BaseUrl"];
        if (string.IsNullOrWhiteSpace(apiBaseUrl))
        {
            result.Errors.Add("ApiClient:BaseUrl is required");
        }
        else if (!Uri.TryCreate(apiBaseUrl, UriKind.Absolute, out _))
        {
            result.Errors.Add("ApiClient:BaseUrl must be a valid URL");
        }
    }

    private string GetSectionNameForType<T>()
    {
        // Use convention-based mapping: remove "Options" suffix from type name
        var typeName = typeof(T).Name;
        if (typeName.EndsWith("Options"))
        {
            return typeName[..^7]; // Remove "Options" suffix
        }
        return typeName;
    }

    private string MaskSensitiveValue(string key, string value)
    {
        var sensitiveKeys = new[] { "password", "secret", "key", "token", "connectionstring" };
        
        if (sensitiveKeys.Any(k => key.ToLowerInvariant().Contains(k)))
        {
            return value.Length > 4 ? $"{value[..4]}****" : "****";
        }

        return value;
    }

    private void LogConfigurationSummary()
    {
        try
        {
            _logger.LogInformation("Configuration Summary:");
            _logger.LogInformation("Application: {Name} v{Version} ({Environment})",
                _configuration["Application:Name"], _configuration["Application:Version"], _configuration["Application:Environment"]);
            _logger.LogInformation("Service Bus: Queue={Queue}, Topic={Topic}, Subscription={Subscription}",
                _configuration["ServiceBus:QueueName"], _configuration["ServiceBus:TopicName"], _configuration["ServiceBus:SubscriptionName"]);
            _logger.LogInformation("API Client: BaseUrl={BaseUrl}, Timeout={Timeout}, RetryCount={RetryCount}",
                _configuration["ApiClient:BaseUrl"], _configuration["ApiClient:Timeout"], _configuration["ApiClient:RetryCount"]);
            _logger.LogInformation("Processing: Batching={Batching}, Deduplication={Deduplication}, Parallel={Parallel}",
                _configuration["Processing:EnableBatching"], _configuration["Processing:EnableDeduplication"], _configuration["Processing:EnableParallelProcessing"]);
            _logger.LogInformation("Monitoring: AppInsights={AppInsights}, Metrics={Metrics}, HealthChecks={HealthChecks}",
                _configuration["Monitoring:EnableApplicationInsights"], _configuration["Monitoring:EnableCustomMetrics"], _configuration["Monitoring:HealthChecks:Enabled"]);
            _logger.LogInformation("Security: Encryption={Encryption}, KeyVault={KeyVault}, Audit={Audit}",
                _configuration["Security:EnableEncryption"], _configuration["Security:EnableKeyVault"], _configuration["Security:EnableAuditLogging"]);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log configuration summary");
        }
    }
}
