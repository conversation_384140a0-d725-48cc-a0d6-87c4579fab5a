# Deployment Guide

## Quick Start

### 1. Build the Application

```bash
# Build the solution
dotnet build --configuration Release

# Publish for deployment
dotnet publish Sub.JakeCRM.ServiceHost --configuration Release --output ./publish
```

### 2. Configure the Application

Update the configuration files in the publish directory:

**appsettings.json** - Production settings:
```json
{
  "ServiceBus": {
    "ConnectionString": "Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key",
    "QueueName": "your-queue-name"
  },
  "ApiClient": {
    "BaseUrl": "https://your-destination-api.com",
    "ApiKey": "your-api-key"
  }
}
```

### 3. Install as Windows Service

Run PowerShell as Administrator:

```powershell
# Install the service
.\install-service.ps1 -ServicePath "C:\path\to\your\publish\Sub.JakeCRM.ServiceHost.exe"

# Or with custom settings
.\install-service.ps1 -ServicePath "C:\path\to\your\publish\Sub.JakeCRM.ServiceHost.exe" -ServiceName "MyCustomServiceName" -DisplayName "My Custom Display Name"
```

### 4. Alternative: Manual Service Installation

```cmd
# Create service
sc create "Sub.JakeCRM.ServiceHost" binPath="C:\path\to\your\publish\Sub.JakeCRM.ServiceHost.exe" DisplayName="Sub.JakeCRM Service Host" start=auto

# Set description
sc description "Sub.JakeCRM.ServiceHost" "Service Bus message processor that submits messages to destination APIs"

# Start service
sc start "Sub.JakeCRM.ServiceHost"
```

## Docker Deployment

### 1. Build Docker Image

```bash
# Build the image
docker build -t sub-jakecrm-servicehost .

# Or using docker-compose
docker-compose build
```

### 2. Configure Environment

Copy `.env.template` to `.env` and update with your settings:

```bash
cp .env.template .env
# Edit .env with your actual values
```

### 3. Run with Docker Compose

```bash
docker-compose up -d
```

## Configuration Options

### Service Bus Configuration

| Setting | Description | Default |
|---------|-------------|---------|
| `ConnectionString` | Service Bus connection string | Required |
| `QueueName` | Queue name to receive from | Required |
| `TopicName` | Topic name (alternative to queue) | Optional |
| `SubscriptionName` | Subscription name (required with topic) | Optional |
| `MaxConcurrentCalls` | Max concurrent message processing | 1 |
| `PrefetchCount` | Number of messages to prefetch | 0 |
| `MaxDeliveryCount` | Max delivery attempts before dead letter | 3 |

### API Client Configuration

| Setting | Description | Default |
|---------|-------------|---------|
| `BaseUrl` | Destination API base URL | Required |
| `Timeout` | Request timeout | 30 seconds |
| `RetryCount` | Number of retry attempts | 3 |
| `RetryDelay` | Delay between retries | 2 seconds |
| `ApiKey` | API key for authentication | Optional |
| `BearerToken` | Bearer token for authentication | Optional |

## Environment Variables

You can override configuration using environment variables:

```bash
# Service Bus
ServiceBus__ConnectionString="your-connection-string"
ServiceBus__QueueName="your-queue"

# API Client
ApiClient__BaseUrl="https://your-api.com"
ApiClient__ApiKey="your-api-key"
```

## Monitoring

### Windows Event Log

The service logs to Windows Event Log under "Application" source.

### Health Checks

Health check endpoint is available when running as a web service.

### Log Levels

Configure logging in `appsettings.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Sub.JakeCRM": "Debug"
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Service won't start**
   - Check Windows Event Log for errors
   - Verify configuration file exists and is valid
   - Ensure proper permissions on installation directory

2. **Service Bus connection errors**
   - Verify connection string format
   - Check network connectivity to Service Bus
   - Ensure proper authentication credentials

3. **API submission failures**
   - Check destination API availability
   - Verify API key/authentication
   - Review network connectivity

### Service Management Commands

```powershell
# Check service status
Get-Service -Name "Sub.JakeCRM.ServiceHost"

# Start service
Start-Service -Name "Sub.JakeCRM.ServiceHost"

# Stop service
Stop-Service -Name "Sub.JakeCRM.ServiceHost"

# Restart service
Restart-Service -Name "Sub.JakeCRM.ServiceHost"

# Remove service
sc.exe delete "Sub.JakeCRM.ServiceHost"
```

### Logs Location

- **Windows Service**: Windows Event Log (Application)
- **Console Mode**: Console output
- **Docker**: Container logs (`docker logs <container-name>`)

## Performance Tuning

### Service Bus Settings

- Increase `MaxConcurrentCalls` for higher throughput
- Adjust `PrefetchCount` based on message size and processing time
- Use sessions for ordered processing if needed

### API Client Settings

- Adjust `Timeout` based on API response times
- Tune `RetryCount` and `RetryDelay` for your error scenarios
- Enable detailed logging for debugging

## Security Considerations

1. **Connection Strings**: Store securely, use Azure Key Vault in production
2. **API Keys**: Rotate regularly, use secure storage
3. **Network**: Use private endpoints where possible
4. **Service Account**: Run service with minimal required permissions
