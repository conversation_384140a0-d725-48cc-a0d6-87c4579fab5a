using Sub.JakeCRM.Domain.Entities;

namespace Sub.JakeCRM.Domain.Interfaces;

/// <summary>
/// Interface for processing messages from Service Bus
/// </summary>
public interface IMessageProcessor
{
    /// <summary>
    /// Processes a message received from Service Bus
    /// </summary>
    /// <param name="message">The message to process</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result</returns>
    Task<ProcessingResult> ProcessMessageAsync(ServiceBusMessage message, CancellationToken cancellationToken = default);
}
