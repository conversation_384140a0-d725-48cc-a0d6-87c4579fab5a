namespace Sub.JakeCRM.Domain.Interfaces;

/// <summary>
/// Interface for making API calls to destination endpoints
/// </summary>
public interface IApiClient
{
    /// <summary>
    /// Submits data to the destination API
    /// </summary>
    /// <param name="endpoint">The API endpoint</param>
    /// <param name="data">The data to submit</param>
    /// <param name="headers">Optional headers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>API response</returns>
    Task<ApiResponse> SubmitAsync(string endpoint, object data, Dictionary<string, string>? headers = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Submits raw JSON data to the destination API
    /// </summary>
    /// <param name="endpoint">The API endpoint</param>
    /// <param name="jsonData">The JSON data to submit</param>
    /// <param name="headers">Optional headers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>API response</returns>
    Task<ApiResponse> SubmitJsonAsync(string endpoint, string jsonData, Dictionary<string, string>? headers = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents an API response
/// </summary>
public class ApiResponse
{
    public bool IsSuccess { get; set; }
    public int StatusCode { get; set; }
    public string? Content { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public TimeSpan ResponseTime { get; set; }
}
