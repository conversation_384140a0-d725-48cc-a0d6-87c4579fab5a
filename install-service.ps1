# PowerShell script to install Sub.JakeCRM Service Host as a Windows Service
# Run this script as Administrator

param(
    [Parameter(Mandatory=$true)]
    [string]$ServicePath,
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceName = "Sub.JakeCRM.ServiceHost",
    
    [Parameter(Mandatory=$false)]
    [string]$DisplayName = "Sub.JakeCRM Service Host",
    
    [Parameter(Mandatory=$false)]
    [string]$Description = "Service Bus message processor that submits messages to destination APIs"
)

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Error "This script must be run as Administrator. Exiting..."
    exit 1
}

# Validate service path
if (-not (Test-Path $ServicePath)) {
    Write-Error "Service executable not found at: $ServicePath"
    exit 1
}

try {
    # Check if service already exists
    $existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
    
    if ($existingService) {
        Write-Host "Service '$ServiceName' already exists. Stopping and removing..." -ForegroundColor Yellow
        
        # Stop the service if it's running
        if ($existingService.Status -eq 'Running') {
            Stop-Service -Name $ServiceName -Force
            Write-Host "Service stopped." -ForegroundColor Green
        }
        
        # Remove the existing service
        sc.exe delete $ServiceName
        Write-Host "Existing service removed." -ForegroundColor Green
        
        # Wait a moment for the service to be fully removed
        Start-Sleep -Seconds 2
    }
    
    # Create the new service
    Write-Host "Creating service '$ServiceName'..." -ForegroundColor Blue
    
    $result = sc.exe create $ServiceName binPath= $ServicePath DisplayName= $DisplayName start= auto
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Service created successfully." -ForegroundColor Green
        
        # Set service description
        sc.exe description $ServiceName $Description
        
        # Set service to restart on failure
        sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/10000/restart/30000
        
        Write-Host "Service configuration completed." -ForegroundColor Green
        
        # Ask if user wants to start the service now
        $startNow = Read-Host "Do you want to start the service now? (y/n)"
        if ($startNow -eq 'y' -or $startNow -eq 'Y') {
            Start-Service -Name $ServiceName
            Write-Host "Service started successfully." -ForegroundColor Green
            
            # Show service status
            Get-Service -Name $ServiceName | Format-Table -AutoSize
        }
        else {
            Write-Host "Service created but not started. You can start it manually using:" -ForegroundColor Yellow
            Write-Host "Start-Service -Name '$ServiceName'" -ForegroundColor Yellow
        }
    }
    else {
        Write-Error "Failed to create service. Exit code: $LASTEXITCODE"
        exit 1
    }
}
catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    exit 1
}

Write-Host ""
Write-Host "Service installation completed!" -ForegroundColor Green
Write-Host "Service Name: $ServiceName" -ForegroundColor Cyan
Write-Host "Display Name: $DisplayName" -ForegroundColor Cyan
Write-Host "Executable Path: $ServicePath" -ForegroundColor Cyan
Write-Host ""
Write-Host "To manage the service, use:" -ForegroundColor Yellow
Write-Host "  Start:   Start-Service -Name '$ServiceName'" -ForegroundColor Yellow
Write-Host "  Stop:    Stop-Service -Name '$ServiceName'" -ForegroundColor Yellow
Write-Host "  Status:  Get-Service -Name '$ServiceName'" -ForegroundColor Yellow
Write-Host "  Remove:  sc.exe delete '$ServiceName'" -ForegroundColor Yellow
