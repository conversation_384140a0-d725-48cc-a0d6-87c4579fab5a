2025-06-07 17:02:42.0825 INFO  [Program] Sub.JakeCRM Service Host initializing... 
2025-06-07 17:02:42.2221 INFO  [Program] Sub.JakeCRM Service Host starting... 
2025-06-07 17:02:42.2510 INFO  [ConfigurationValidationService] Starting configuration validation... 
2025-06-07 17:02:42.2854 INFO  [ConfigurationService] Configuration Summary: 
2025-06-07 17:02:42.2854 INFO  [ConfigurationService] Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production) 
2025-06-07 17:02:42.2976 INFO  [ConfigurationService] Service Bus: Queue=your-queue-name, Topic=, Subscription= 
2025-06-07 17:02:42.3067 INFO  [ConfigurationService] API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3 
2025-06-07 17:02:42.3067 INFO  [ConfigurationService] Processing: Batching=False, Deduplication=False, Parallel=True 
2025-06-07 17:02:42.3067 INFO  [ConfigurationService] Monitoring: AppInsights=False, Metrics=True, HealthChecks=True 
2025-06-07 17:02:42.3067 INFO  [ConfigurationService] Security: Encryption=False, KeyVault=False, Audit=True 
2025-06-07 17:02:42.3067 INFO  [ConfigurationService] Configuration validation passed successfully 
2025-06-07 17:02:42.3195 INFO  [ConfigurationValidationService] Configuration validation completed successfully 
2025-06-07 17:02:42.3195 INFO  [ConfigurationValidationService] === Configuration Summary === 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] Application:Name: Sub.JakeCRM.ServiceHost 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] Application:Version: 1.0.0 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] Application:Environment: Production 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] ServiceBus:QueueName: your-queue-name 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] ServiceBus:TopicName:  
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] ServiceBus:MaxConcurrentCalls: 1 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] ApiClient:BaseUrl: https://your-destination-api.com 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] ApiClient:Timeout: 00:00:30 
2025-06-07 17:02:42.3344 INFO  [ConfigurationValidationService] ApiClient:RetryCount: 3 
2025-06-07 17:02:42.3507 INFO  [ConfigurationValidationService] Processing:EnableBatching: False 
2025-06-07 17:02:42.3507 INFO  [ConfigurationValidationService] Processing:EnableParallelProcessing: True 
2025-06-07 17:02:42.3507 INFO  [ConfigurationValidationService] Monitoring:EnableCustomMetrics: True 
2025-06-07 17:02:42.3507 INFO  [ConfigurationValidationService] Monitoring:HealthChecks:Enabled: True 
2025-06-07 17:02:42.3507 INFO  [ConfigurationValidationService] Security:EnableEncryption: False 
2025-06-07 17:02:42.3507 INFO  [ConfigurationValidationService] Security:EnableAuditLogging: True 
2025-06-07 17:02:42.3507 INFO  [ConfigurationValidationService] === End Configuration Summary === 
2025-06-07 17:02:42.3507 INFO  [MessageProcessingBackgroundService] Message Processing Background Service starting... 
2025-06-07 17:02:42.4010 INFO  [ServiceBusReceiver] Starting Service Bus receiver... 
2025-06-07 17:02:42.4235 INFO  [ServiceBusReceiver] Created processor for queue: your-queue-name 
2025-06-07 17:02:42.4990 INFO  [ServiceBusReceiver] Service Bus receiver started successfully 
2025-06-07 17:02:42.4990 INFO  [MessageProcessingBackgroundService] Message Processing Background Service started successfully 
2025-06-07 17:02:55.0821 ERROR [ServiceBusReceiver] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-06-07 17:03:07.5320 ERROR [ServiceBusReceiver] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-06-07 17:05:19.8198 INFO  [Program] Sub.JakeCRM Service Host initializing... 
2025-06-07 17:05:20.2305 INFO  [Program] Sub.JakeCRM Service Host starting... 
2025-06-07 17:05:20.2686 INFO  [ConfigurationValidationService] Starting configuration validation... 
2025-06-07 17:05:20.3137 INFO  [ConfigurationService] Configuration Summary: 
2025-06-07 17:05:20.3239 INFO  [ConfigurationService] Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production) 
2025-06-07 17:05:20.3269 INFO  [ConfigurationService] Service Bus: Queue=your-queue-name, Topic=, Subscription= 
2025-06-07 17:05:20.3269 INFO  [ConfigurationService] API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3 
2025-06-07 17:05:20.3401 INFO  [ConfigurationService] Processing: Batching=False, Deduplication=False, Parallel=True 
2025-06-07 17:05:20.3401 INFO  [ConfigurationService] Monitoring: AppInsights=False, Metrics=True, HealthChecks=True 
2025-06-07 17:05:20.3401 INFO  [ConfigurationService] Security: Encryption=False, KeyVault=False, Audit=True 
2025-06-07 17:05:20.3401 INFO  [ConfigurationService] Configuration validation passed successfully 
2025-06-07 17:05:20.3545 INFO  [ConfigurationValidationService] Configuration validation completed successfully 
2025-06-07 17:05:20.3545 INFO  [ConfigurationValidationService] === Configuration Summary === 
2025-06-07 17:05:20.3722 INFO  [ConfigurationValidationService] Application:Name: Sub.JakeCRM.ServiceHost 
2025-06-07 17:05:20.3722 INFO  [ConfigurationValidationService] Application:Version: 1.0.0 
2025-06-07 17:05:20.3722 INFO  [ConfigurationValidationService] Application:Environment: Production 
2025-06-07 17:05:20.3722 INFO  [ConfigurationValidationService] ServiceBus:QueueName: your-queue-name 
2025-06-07 17:05:20.3811 INFO  [ConfigurationValidationService] ServiceBus:TopicName:  
2025-06-07 17:05:20.3811 INFO  [ConfigurationValidationService] ServiceBus:MaxConcurrentCalls: 1 
2025-06-07 17:05:20.3811 INFO  [ConfigurationValidationService] ApiClient:BaseUrl: https://your-destination-api.com 
2025-06-07 17:05:20.3811 INFO  [ConfigurationValidationService] ApiClient:Timeout: 00:00:30 
2025-06-07 17:05:20.3811 INFO  [ConfigurationValidationService] ApiClient:RetryCount: 3 
2025-06-07 17:05:20.3811 INFO  [ConfigurationValidationService] Processing:EnableBatching: False 
2025-06-07 17:05:20.3811 INFO  [ConfigurationValidationService] Processing:EnableParallelProcessing: True 
2025-06-07 17:05:20.3988 INFO  [ConfigurationValidationService] Monitoring:EnableCustomMetrics: True 
2025-06-07 17:05:20.3988 INFO  [ConfigurationValidationService] Monitoring:HealthChecks:Enabled: True 
2025-06-07 17:05:20.3988 INFO  [ConfigurationValidationService] Security:EnableEncryption: False 
2025-06-07 17:05:20.3988 INFO  [ConfigurationValidationService] Security:EnableAuditLogging: True 
2025-06-07 17:05:20.3988 INFO  [ConfigurationValidationService] === End Configuration Summary === 
2025-06-07 17:05:20.3988 INFO  [MessageProcessingBackgroundService] Message Processing Background Service starting... 
2025-06-07 17:05:20.5044 INFO  [ServiceBusReceiver] Starting Service Bus receiver... 
2025-06-07 17:05:20.5520 INFO  [ServiceBusReceiver] Created processor for queue: your-queue-name 
2025-06-07 17:05:20.6469 INFO  [ServiceBusReceiver] Service Bus receiver started successfully 
2025-06-07 17:05:20.6493 INFO  [MessageProcessingBackgroundService] Message Processing Background Service started successfully 
2025-06-07 17:05:33.3305 ERROR [ServiceBusReceiver] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-06-07 17:05:44.8588 ERROR [ServiceBusReceiver] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-06-07 17:05:57.3728 ERROR [ServiceBusReceiver] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-06-07 17:09:06.2259 INFO  [Program] Sub.JakeCRM Service Host initializing... 
2025-06-07 17:09:06.9830 INFO  [Program] Sub.JakeCRM Service Host starting... 
2025-06-07 17:09:07.0062 INFO  [ConfigurationValidationService] Starting configuration validation... 
2025-06-07 17:09:07.0366 INFO  [ConfigurationService] Configuration Summary: 
2025-06-07 17:09:07.0490 INFO  [ConfigurationService] Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production) 
2025-06-07 17:09:07.0490 INFO  [ConfigurationService] Service Bus: Queue=your-queue-name, Topic=, Subscription= 
2025-06-07 17:09:07.0637 INFO  [ConfigurationService] API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3 
2025-06-07 17:09:07.0637 INFO  [ConfigurationService] Processing: Batching=False, Deduplication=False, Parallel=True 
2025-06-07 17:09:07.0693 INFO  [ConfigurationService] Monitoring: AppInsights=False, Metrics=True, HealthChecks=True 
2025-06-07 17:09:07.0693 INFO  [ConfigurationService] Security: Encryption=False, KeyVault=False, Audit=True 
2025-06-07 17:09:07.0693 INFO  [ConfigurationService] Configuration validation passed successfully 
2025-06-07 17:09:07.0693 INFO  [ConfigurationValidationService] Configuration validation completed successfully 
2025-06-07 17:09:07.0693 INFO  [ConfigurationValidationService] === Configuration Summary === 
2025-06-07 17:09:07.0962 INFO  [ConfigurationValidationService] Application:Name: Sub.JakeCRM.ServiceHost 
2025-06-07 17:09:07.0962 INFO  [ConfigurationValidationService] Application:Version: 1.0.0 
2025-06-07 17:09:07.1023 INFO  [ConfigurationValidationService] Application:Environment: Production 
2025-06-07 17:09:07.1023 INFO  [ConfigurationValidationService] ServiceBus:QueueName: your-queue-name 
2025-06-07 17:09:07.1023 INFO  [ConfigurationValidationService] ServiceBus:TopicName:  
2025-06-07 17:09:07.1023 INFO  [ConfigurationValidationService] ServiceBus:MaxConcurrentCalls: 1 
2025-06-07 17:09:07.1023 INFO  [ConfigurationValidationService] ApiClient:BaseUrl: https://your-destination-api.com 
2025-06-07 17:09:07.1023 INFO  [ConfigurationValidationService] ApiClient:Timeout: 00:00:30 
2025-06-07 17:09:07.1160 INFO  [ConfigurationValidationService] ApiClient:RetryCount: 3 
2025-06-07 17:09:07.1160 INFO  [ConfigurationValidationService] Processing:EnableBatching: False 
2025-06-07 17:09:07.1160 INFO  [ConfigurationValidationService] Processing:EnableParallelProcessing: True 
2025-06-07 17:09:07.1160 INFO  [ConfigurationValidationService] Monitoring:EnableCustomMetrics: True 
2025-06-07 17:09:07.1160 INFO  [ConfigurationValidationService] Monitoring:HealthChecks:Enabled: True 
2025-06-07 17:09:07.1160 INFO  [ConfigurationValidationService] Security:EnableEncryption: False 
2025-06-07 17:09:07.1160 INFO  [ConfigurationValidationService] Security:EnableAuditLogging: True 
2025-06-07 17:09:07.1329 INFO  [ConfigurationValidationService] === End Configuration Summary === 
2025-06-07 17:09:07.1329 INFO  [MessageProcessingBackgroundService] Message Processing Background Service starting... 
2025-06-07 17:09:07.1803 INFO  [ServiceBusReceiver] Starting Service Bus receiver... 
2025-06-07 17:09:07.2072 INFO  [ServiceBusReceiver] Created processor for queue: your-queue-name 
2025-06-07 17:09:07.2752 INFO  [ServiceBusReceiver] Service Bus receiver started successfully 
2025-06-07 17:09:07.2752 INFO  [MessageProcessingBackgroundService] Message Processing Background Service started successfully 
2025-06-07 17:09:19.9182 ERROR [ServiceBusReceiver] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-06-07 17:09:21.8702 INFO  [Program] Sub.JakeCRM Service Host initializing... 
2025-06-07 17:09:22.3166 INFO  [Program] Sub.JakeCRM Service Host starting... 
2025-06-07 17:09:22.3641 INFO  [ConfigurationValidationService] Starting configuration validation... 
2025-06-07 17:09:22.4430 INFO  [ConfigurationService] Configuration Summary: 
2025-06-07 17:09:22.4577 INFO  [ConfigurationService] Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production) 
2025-06-07 17:09:22.4632 INFO  [ConfigurationService] Service Bus: Queue=your-queue-name, Topic=, Subscription= 
2025-06-07 17:09:22.4755 INFO  [ConfigurationService] API Client: BaseUrl=https://your-destination-api.com, Timeout=30s, RetryCount=3 
2025-06-07 17:09:22.4755 INFO  [ConfigurationService] Processing: Batching=False, Deduplication=False, Parallel=True 
2025-06-07 17:09:22.4909 INFO  [ConfigurationService] Monitoring: AppInsights=False, Metrics=True, HealthChecks=True 
2025-06-07 17:09:22.4909 INFO  [ConfigurationService] Security: Encryption=False, KeyVault=False, Audit=True 
2025-06-07 17:09:22.5070 INFO  [ConfigurationService] Configuration validation passed successfully 
2025-06-07 17:09:22.5070 INFO  [ConfigurationValidationService] Configuration validation completed successfully 
2025-06-07 17:09:22.5070 INFO  [ConfigurationValidationService] === Configuration Summary === 
2025-06-07 17:09:22.5323 INFO  [ConfigurationValidationService] Application:Name: Sub.JakeCRM.ServiceHost 
2025-06-07 17:09:22.5323 INFO  [ConfigurationValidationService] Application:Version: 1.0.0 
2025-06-07 17:09:22.5402 INFO  [ConfigurationValidationService] Application:Environment: Production 
2025-06-07 17:09:22.5402 INFO  [ConfigurationValidationService] ServiceBus:QueueName: your-queue-name 
2025-06-07 17:09:22.5402 INFO  [ConfigurationValidationService] ServiceBus:TopicName:  
2025-06-07 17:09:22.5530 INFO  [ConfigurationValidationService] ServiceBus:MaxConcurrentCalls: 1 
2025-06-07 17:09:22.5530 INFO  [ConfigurationValidationService] ApiClient:BaseUrl: https://your-destination-api.com 
2025-06-07 17:09:22.5530 INFO  [ConfigurationValidationService] ApiClient:Timeout: 00:00:30 
2025-06-07 17:09:22.5530 INFO  [ConfigurationValidationService] ApiClient:RetryCount: 3 
2025-06-07 17:09:22.5530 INFO  [ConfigurationValidationService] Processing:EnableBatching: False 
2025-06-07 17:09:22.5530 INFO  [ConfigurationValidationService] Processing:EnableParallelProcessing: True 
2025-06-07 17:09:22.5530 INFO  [ConfigurationValidationService] Monitoring:EnableCustomMetrics: True 
2025-06-07 17:09:22.5689 INFO  [ConfigurationValidationService] Monitoring:HealthChecks:Enabled: True 
2025-06-07 17:09:22.5689 INFO  [ConfigurationValidationService] Security:EnableEncryption: False 
2025-06-07 17:09:22.5689 INFO  [ConfigurationValidationService] Security:EnableAuditLogging: True 
2025-06-07 17:09:22.5689 INFO  [ConfigurationValidationService] === End Configuration Summary === 
2025-06-07 17:09:22.5878 INFO  [MessageProcessingBackgroundService] Message Processing Background Service starting... 
2025-06-07 17:09:22.6942 INFO  [ServiceBusReceiver] Starting Service Bus receiver... 
2025-06-07 17:09:22.7532 INFO  [ServiceBusReceiver] Created processor for queue: your-queue-name 
2025-06-07 17:09:22.8849 INFO  [ServiceBusReceiver] Service Bus receiver started successfully 
2025-06-07 17:09:22.8879 INFO  [MessageProcessingBackgroundService] Message Processing Background Service started successfully 
2025-06-07 17:09:35.5214 ERROR [ServiceBusReceiver] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
