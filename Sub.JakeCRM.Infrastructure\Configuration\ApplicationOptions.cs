namespace Sub.JakeCRM.Infrastructure.Configuration;

/// <summary>
/// General application configuration options
/// </summary>
public class ApplicationOptions
{
    public const string SectionName = "Application";

    /// <summary>
    /// Application name for logging and identification
    /// </summary>
    public string Name { get; set; } = "Sub.JakeCRM.ServiceHost";

    /// <summary>
    /// Application version
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// Application environment (Development, Staging, Production)
    /// </summary>
    public string Environment { get; set; } = "Production";

    /// <summary>
    /// Enable performance monitoring and metrics collection
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// Enable detailed request/response logging
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Maximum number of messages to process per minute (0 = unlimited)
    /// </summary>
    public int MaxMessagesPerMinute { get; set; } = 0;

    /// <summary>
    /// Graceful shutdown timeout in seconds
    /// </summary>
    public int ShutdownTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Enable health checks
    /// </summary>
    public bool EnableHealthChecks { get; set; } = true;

    /// <summary>
    /// Health check interval in seconds
    /// </summary>
    public int HealthCheckIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// Custom application properties
    /// </summary>
    public Dictionary<string, string> CustomProperties { get; set; } = new();
}
