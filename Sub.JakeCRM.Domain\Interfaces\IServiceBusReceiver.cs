using Sub.JakeCRM.Domain.Entities;

namespace Sub.JakeCRM.Domain.Interfaces;

/// <summary>
/// Interface for receiving messages from Service Bus
/// </summary>
public interface IServiceBusReceiver
{
    /// <summary>
    /// Starts receiving messages from Service Bus
    /// </summary>
    /// <param name="messageHandler">Handler for processing received messages</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task StartReceivingAsync(Func<ServiceBusMessage, CancellationToken, Task<ProcessingResult>> messageHandler, CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops receiving messages from Service Bus
    /// </summary>
    Task StopReceivingAsync();

    /// <summary>
    /// Completes a message (removes it from the queue)
    /// </summary>
    /// <param name="lockToken">The lock token of the message</param>
    Task CompleteMessageAsync(string lockToken);

    /// <summary>
    /// Abandons a message (returns it to the queue for reprocessing)
    /// </summary>
    /// <param name="lockToken">The lock token of the message</param>
    Task AbandonMessageAsync(string lockToken);

    /// <summary>
    /// Dead letters a message (moves it to the dead letter queue)
    /// </summary>
    /// <param name="lockToken">The lock token of the message</param>
    /// <param name="reason">Reason for dead lettering</param>
    /// <param name="errorDescription">Error description</param>
    Task DeadLetterMessageAsync(string lockToken, string reason, string errorDescription);
}
