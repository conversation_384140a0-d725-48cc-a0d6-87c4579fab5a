# NLog Configuration Guide

This document describes the NLog logging configuration for the Sub.JakeCRM Service Host application.

## Overview

The application uses NLog for comprehensive logging with support for:
- ✅ **Multiple log targets** (Console, File, JSON, Event Log)
- ✅ **Environment-specific configurations**
- ✅ **Structured logging with correlation IDs**
- ✅ **Configurable log levels and formatting**
- ✅ **Automatic log rotation and archiving**
- ✅ **Performance optimizations**

## Configuration Files

### Primary NLog Configuration

- **`nlog.config`** - Production logging configuration
- **`nlog.Development.config`** - Development logging configuration

### Application Settings Integration

NLog settings can also be configured through `appsettings.json`:

```json
{
  "NLog": {
    "AutoReload": true,
    "InternalLogLevel": "Info",
    "EnableInternalLogging": true,
    "InternalLogFile": "logs/internal-nlog.txt",
    "LogDirectory": "logs",
    "MaxArchiveFiles": 30,
    "ArchiveEvery": "Day",
    "EnableConsoleLogging": true,
    "EnableFileLogging": true,
    "EnableEventLogLogging": true,
    "EnableJsonLogging": true,
    "ConsoleMinLevel": "Info",
    "FileMinLevel": "Info",
    "EventLogMinLevel": "Error",
    "JsonMinLevel": "Info"
  }
}
```

## Log Targets

### 1. Console Target

**Purpose**: Real-time log viewing during development and debugging

**Features**:
- Color-coded log levels
- Compact timestamp format
- Logger name abbreviation

**Configuration**:
```xml
<target xsi:type="ColoredConsole" name="console"
        layout="${time:format=HH\:mm\:ss.fff} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}">
  <highlight-row condition="level == LogLevel.Debug" foregroundColor="DarkGray" />
  <highlight-row condition="level == LogLevel.Info" foregroundColor="Gray" />
  <highlight-row condition="level == LogLevel.Warn" foregroundColor="Yellow" />
  <highlight-row condition="level == LogLevel.Error" foregroundColor="Red" />
  <highlight-row condition="level == LogLevel.Fatal" foregroundColor="Red" backgroundColor="White" />
</target>
```

### 2. File Targets

**Purpose**: Persistent logging for production monitoring and troubleshooting

**Types**:
- **All Logs** (`all-{date}.log`) - All application logs
- **Application Logs** (`app-{date}.log`) - Sub.JakeCRM specific logs
- **Error Logs** (`errors-{date}.log`) - Error and fatal logs only

**Features**:
- Daily log rotation
- Automatic archiving (30 days retention)
- Concurrent write support
- Configurable buffer sizes

### 3. JSON Structured Logs

**Purpose**: Machine-readable logs for log aggregation systems

**Format**:
```json
{
  "timestamp": "2024-01-15 17:02:42.0825",
  "level": "INFO",
  "logger": "Sub.JakeCRM.ServiceHost.Program",
  "thread": "1",
  "message": "Sub.JakeCRM Service Host starting...",
  "exception": "",
  "properties": {
    "application": "Sub.JakeCRM.ServiceHost",
    "environment": "Production",
    "version": "1.0.0",
    "machineName": "SERVER01",
    "processId": "12345"
  }
}
```

### 4. Event Log Target

**Purpose**: Windows Event Log integration for system monitoring

**Configuration**:
- Source: `Sub.JakeCRM.ServiceHost`
- Log: `Application`
- Minimum Level: `Error`

## Log Levels

### Production Levels
- **Error/Fatal**: Critical errors and exceptions
- **Warning**: Important issues that don't stop execution
- **Information**: General application flow and key events
- **Debug**: Disabled in production

### Development Levels
- **Debug**: Detailed execution flow
- **Information**: General application events
- **Warning**: Potential issues
- **Error/Fatal**: Errors and exceptions

## Log Layout Patterns

### Standard Layout
```
${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] [${threadid}] ${message} ${exception:format=tostring}
```

**Example Output**:
```
2024-01-15 17:02:42.0825 INFO  [Program] [1] Sub.JakeCRM Service Host starting...
```

### Console Layout
```
${time:format=HH\:mm\:ss.fff} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}
```

**Example Output**:
```
17:02:42.082 INFO  [Program] Sub.JakeCRM Service Host starting...
```

## Log File Organization

```
logs/
├── all-2024-01-15.log              # All logs
├── app-2024-01-15.log              # Application logs
├── errors-2024-01-15.log           # Error logs
├── structured-2024-01-15.json      # JSON structured logs
├── internal-nlog.txt               # NLog internal logs
└── archives/                       # Archived logs
    ├── all-1.log
    ├── app-1.log
    └── errors-1.log
```

## Configuration Examples

### Development Configuration

```json
{
  "NLog": {
    "InternalLogLevel": "Debug",
    "LogDirectory": "logs",
    "MaxArchiveFiles": 7,
    "EnableConsoleLogging": true,
    "EnableFileLogging": true,
    "EnableEventLogLogging": false,
    "EnableJsonLogging": false,
    "ConsoleMinLevel": "Debug",
    "FileMinLevel": "Debug"
  }
}
```

### Production Configuration

```json
{
  "NLog": {
    "InternalLogLevel": "Info",
    "LogDirectory": "logs",
    "MaxArchiveFiles": 30,
    "EnableConsoleLogging": true,
    "EnableFileLogging": true,
    "EnableEventLogLogging": true,
    "EnableJsonLogging": true,
    "ConsoleMinLevel": "Info",
    "FileMinLevel": "Info",
    "EventLogMinLevel": "Error",
    "JsonMinLevel": "Info"
  }
}
```

### High-Performance Configuration

```json
{
  "NLog": {
    "EnableAsyncLogging": true,
    "AsyncQueueLimit": 10000,
    "AsyncOverflowAction": "Block",
    "EnableConcurrentWrites": true,
    "KeepFileOpen": true,
    "FileBufferSize": 65536,
    "FlushTimeoutMs": 1000
  }
}
```

## Environment Variables

Override NLog settings using environment variables:

```bash
# Log levels
NLog__ConsoleMinLevel="Debug"
NLog__FileMinLevel="Info"
NLog__EventLogMinLevel="Error"

# File settings
NLog__LogDirectory="C:\Logs\Sub.JakeCRM"
NLog__MaxArchiveFiles="60"
NLog__EnableCompression="true"

# Performance settings
NLog__EnableAsyncLogging="true"
NLog__FileBufferSize="32768"
```

## Monitoring and Troubleshooting

### Internal NLog Logging

Enable internal logging to troubleshoot NLog configuration issues:

```json
{
  "NLog": {
    "EnableInternalLogging": true,
    "InternalLogLevel": "Debug",
    "InternalLogFile": "logs/internal-nlog.txt"
  }
}
```

### Performance Monitoring

Monitor log performance using:
- File write performance
- Memory usage
- Queue sizes (for async logging)
- Archive file counts

### Common Issues

1. **Log files not created**
   - Check directory permissions
   - Verify log directory exists
   - Check internal NLog logs

2. **Poor performance**
   - Enable async logging
   - Increase buffer sizes
   - Use concurrent writes

3. **Missing logs**
   - Check log level configuration
   - Verify logger name patterns
   - Check file rotation settings

## Best Practices

### Development
- Use colored console output
- Enable debug logging
- Keep shorter archive retention
- Disable event log and JSON logging

### Production
- Use structured JSON logging
- Enable event log for critical errors
- Configure appropriate log levels
- Set up log monitoring and alerting
- Use async logging for high throughput

### Security
- Mask sensitive data in logs
- Secure log file access
- Rotate logs regularly
- Monitor log file sizes

## Integration with Application

### Logger Usage

```csharp
public class MyService
{
    private readonly ILogger<MyService> _logger;

    public MyService(ILogger<MyService> logger)
    {
        _logger = logger;
    }

    public async Task ProcessAsync()
    {
        _logger.LogInformation("Starting process...");
        
        try
        {
            // Process logic
            _logger.LogDebug("Processing step completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Process failed");
            throw;
        }
        
        _logger.LogInformation("Process completed successfully");
    }
}
```

### Structured Logging

```csharp
_logger.LogInformation("Processing message {MessageId} for customer {CustomerId}", 
    messageId, customerId);

_logger.LogWarning("API call failed with status {StatusCode} for endpoint {Endpoint}", 
    response.StatusCode, endpoint);
```

## Log Analysis

### Useful Log Queries

**Find all errors in the last hour**:
```bash
grep "ERROR" logs/app-$(date +%Y-%m-%d).log | tail -100
```

**Monitor real-time logs**:
```bash
tail -f logs/app-$(date +%Y-%m-%d).log
```

**Parse JSON logs**:
```bash
jq '.message' logs/structured-$(date +%Y-%m-%d).json
```

The NLog integration provides enterprise-grade logging capabilities with flexible configuration, multiple output targets, and excellent performance characteristics suitable for both development and production environments.
